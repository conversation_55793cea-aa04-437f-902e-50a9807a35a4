{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\nlet RequirementManagementComponent = class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    this.currentTab = 0; // 追蹤當前選中的 tab\n    // Tab 切換事件處理\n    this.isFirstTabChange = true;\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CGroupName = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        if (this.currentTab === 0) {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        } else {\n          this.getListRequirementRequest.CBuildCaseID = 0;\n        }\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 根據當前 tab 決定是否需要驗證建案名稱\n    if (this.currentTab === 0) {\n      // 建案頁面需要驗證建案名稱\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    }\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 根據當前 tab 決定是否需要建案ID\n    if (this.currentTab === 0) {\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\n      if (this.currentBuildCase != 0) {\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    } else {\n      // 模板頁面 - 設定建案ID為0\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\n    if (this.currentTab === 1) {\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 只在建案 tab 下且有建案時才查詢\n      if (this.currentTab === 0 && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      } else if (this.currentTab === 1) {\n        this.getListRequirementRequest.CBuildCaseID = 0;\n        this.getList();\n      }\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\n    if (this.currentTab === 1) {\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      // 建案頁面的邏輯保持不變\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n      }\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          // 將 API 返回的數據轉換為 SelectableRequirement 並初始化 selected 屬性\n          this.requirementList = res.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n          this.totalRecords = res.TotalItems;\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CGroupName = res.Entries.CGroupName;\n          this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          // TODO: 等後端API更新後啟用這行\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  onTabChange(event) {\n    // 避免頁面初始化時自動觸發重複查詢\n    if (this.isFirstTabChange) {\n      this.isFirstTabChange = false;\n      return;\n    }\n    // 根據 tabTitle 來判斷當前頁面\n    if (event.tabTitle === '共用') {\n      this.currentTab = 1;\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      this.currentTab = 0;\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    }\n    this.getList();\n  }\n  // 新增模板\n  addTemplate(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 模板設定建案ID為0\n    this.saveRequirement.CBuildCaseID = 0;\n    this.dialogService.open(dialog);\n  }\n  // 編輯模板\n  onEditTemplate(data, dialog) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this2.isNew = false;\n      try {\n        yield _this2.getData();\n        _this2.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get template data\", error);\n      }\n    })();\n  }\n  // 保存模板\n  saveTemplate(ref) {\n    // 模板驗證（不包含建案名稱）\n    this.valid.clear();\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 確保模板建案ID為0\n    const templateData = {\n      ...this.saveRequirement\n    };\n    templateData.CBuildCaseID = 0;\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: templateData\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  openTemplateViewer(templateViewerDialog) {\n    this.dialogService.open(templateViewerDialog);\n  }\n  onSelectTemplate(tpl) {\n    // 查看模板邏輯\n    alert('查看模板: ' + tpl.TemplateName);\n  }\n  // 獲取選中的需求項目\n  getSelectedRequirements() {\n    return this.requirementList.filter(req => req.selected);\n  }\n  // 選中狀態變更處理\n  onRequirementSelectionChange() {\n    // 可以在這裡添加額外的邏輯，比如更新選中計數等\n  }\n  // 全選功能\n  selectAllRequirements() {\n    this.requirementList.forEach(req => req.selected = true);\n  }\n  // 清除所有選擇\n  clearAllSelections() {\n    this.requirementList.forEach(req => req.selected = false);\n  }\n  // 檢查是否全選\n  isAllSelected() {\n    return this.requirementList.length > 0 && this.requirementList.every(req => req.selected);\n  }\n  // 檢查是否部分選中（用於 indeterminate 狀態）\n  isIndeterminate() {\n    const selectedCount = this.requirementList.filter(req => req.selected).length;\n    return selectedCount > 0 && selectedCount < this.requirementList.length;\n  }\n  // 切換全選狀態\n  toggleSelectAll(event) {\n    const isChecked = event.target.checked;\n    this.requirementList.forEach(req => req.selected = isChecked);\n  }\n  // 打開模板創建器\n  openTemplateCreator(templateCreatorDialog) {\n    const selectedRequirements = this.getSelectedRequirements();\n    if (selectedRequirements.length === 0) {\n      this.message.showErrorMSG('請先選擇要加入模板的項目');\n      return;\n    }\n    this.dialogService.open(templateCreatorDialog);\n  }\n};\nRequirementManagementComponent = __decorate([Component({\n  selector: 'app-requirement-management',\n  standalone: true,\n  imports: [NbCardModule, BreadcrumbComponent, NbInputModule, FormsModule, NbSelectModule, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, FormGroupComponent, NumberWithCommasPipe, NbTabsetModule, TemplateViewerComponent],\n  templateUrl: './requirement-management.component.html',\n  styleUrl: './requirement-management.component.scss'\n})], RequirementManagementComponent);\nexport { RequirementManagementComponent };", "map": {"version": 3, "names": ["Component", "NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "NbTabsetModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "TemplateViewerComponent", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "getListRequirementRequest", "getRequirementRequest", "buildCaseList", "requirementList", "saveRequirement", "CHouseType", "statusOptions", "value", "label", "houseType", "getEnumOptions", "isNew", "currentBuildCase", "currentTab", "isFirstTabChange", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "CStatus", "CIsShow", "CRequirement", "CGroupName", "map", "type", "resetSearch", "length", "setTimeout", "CBuildCaseID", "cID", "getList", "getHouseType", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "CSort", "CUnitPrice", "CUnit", "errorMessages", "CRemark", "add", "dialog", "open", "onEdit", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "save", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "close", "onDelete", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "item", "selected", "TotalItems", "apiRequirementGetDataPost$Json", "onHouseTypeChange", "checked", "includes", "filter", "v", "getCIsShowText", "onTabChange", "event", "tabTitle", "addTemplate", "onEditTemplate", "_this2", "saveTemplate", "templateData", "openTemplateViewer", "templateViewerDialog", "onSelectTemplate", "tpl", "alert", "TemplateName", "getSelectedRequirements", "req", "onRequirementSelectionChange", "selectAllRequirements", "clearAllSelections", "isAllSelected", "every", "isIndeterminate", "selectedCount", "toggleSelectAll", "isChecked", "target", "openTemplateCreator", "templateCreatorDialog", "selectedRequirements", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrl"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { Template, TemplateDetail, TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\r\n\r\n// 擴展 GetRequirement 接口以支持選中狀態\r\ninterface SelectableRequirement extends GetRequirement {\r\n  selected?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true, imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    NbTabsetModule,\r\n    TemplateViewerComponent\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: SelectableRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n  currentTab = 0; // 追蹤當前選中的 tab\r\n\r\n\r\n\r\n  override ngOnInit(): void { }\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CGroupName = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        if (this.currentTab === 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        } else {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n        }\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    // 根據當前 tab 決定是否需要驗證建案名稱\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面需要驗證建案名稱\r\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    }\r\n\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 根據當前 tab 決定是否需要建案ID\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n      if (this.currentBuildCase != 0) {\r\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    } else {\r\n      // 模板頁面 - 設定建案ID為0\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: SelectableRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: SelectableRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 只在建案 tab 下且有建案時才查詢\r\n        if (this.currentTab === 0 && this.buildCaseList.length > 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n          this.getList();\r\n        } else if (this.currentTab === 1) {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n          this.getList();\r\n        }\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\r\n    if (this.currentTab === 1) {\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      // 建案頁面的邏輯保持不變\r\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n      }\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            // 將 API 返回的數據轉換為 SelectableRequirement 並初始化 selected 屬性\r\n            this.requirementList = res.Entries.map(item => ({\r\n              ...item,\r\n              selected: false\r\n            }));\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        }\r\n      })\r\n  } getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CGroupName = res.Entries.CGroupName;\r\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            // TODO: 等後端API更新後啟用這行\r\n            this.saveRequirement.CIsShow = (res.Entries as any).CIsShow || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  // Tab 切換事件處理\r\n  private isFirstTabChange = true;\r\n  onTabChange(event: any) {\r\n    // 避免頁面初始化時自動觸發重複查詢\r\n    if (this.isFirstTabChange) {\r\n      this.isFirstTabChange = false;\r\n      return;\r\n    }\r\n    // 根據 tabTitle 來判斷當前頁面\r\n    if (event.tabTitle === '共用') {\r\n      this.currentTab = 1;\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      this.currentTab = 0;\r\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\r\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    }\r\n    this.getList();\r\n  }\r\n\r\n  // 新增模板\r\n  addTemplate(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n    // 模板設定建案ID為0\r\n    this.saveRequirement.CBuildCaseID = 0;\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 編輯模板\r\n  async onEditTemplate(data: SelectableRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get template data\", error);\r\n    }\r\n  }\r\n\r\n  // 保存模板\r\n  saveTemplate(ref: any) {\r\n    // 模板驗證（不包含建案名稱）\r\n    this.valid.clear();\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 確保模板建案ID為0\r\n    const templateData = { ...this.saveRequirement };\r\n    templateData.CBuildCaseID = 0;\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: templateData\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  openTemplateViewer(templateViewerDialog: TemplateRef<any>) {\r\n    this.dialogService.open(templateViewerDialog);\r\n  }\r\n\r\n  onSelectTemplate(tpl: Template) {\r\n    // 查看模板邏輯\r\n    alert('查看模板: ' + tpl.TemplateName);\r\n  }\r\n\r\n  // 獲取選中的需求項目\r\n  getSelectedRequirements(): SelectableRequirement[] {\r\n    return this.requirementList.filter(req => req.selected);\r\n  }\r\n\r\n  // 選中狀態變更處理\r\n  onRequirementSelectionChange() {\r\n    // 可以在這裡添加額外的邏輯，比如更新選中計數等\r\n  }\r\n\r\n  // 全選功能\r\n  selectAllRequirements() {\r\n    this.requirementList.forEach(req => req.selected = true);\r\n  }\r\n\r\n  // 清除所有選擇\r\n  clearAllSelections() {\r\n    this.requirementList.forEach(req => req.selected = false);\r\n  }\r\n\r\n  // 檢查是否全選\r\n  isAllSelected(): boolean {\r\n    return this.requirementList.length > 0 && this.requirementList.every(req => req.selected);\r\n  }\r\n\r\n  // 檢查是否部分選中（用於 indeterminate 狀態）\r\n  isIndeterminate(): boolean {\r\n    const selectedCount = this.requirementList.filter(req => req.selected).length;\r\n    return selectedCount > 0 && selectedCount < this.requirementList.length;\r\n  }\r\n\r\n  // 切換全選狀態\r\n  toggleSelectAll(event: any) {\r\n    const isChecked = event.target.checked;\r\n    this.requirementList.forEach(req => req.selected = isChecked);\r\n  }\r\n\r\n  // 打開模板創建器\r\n  openTemplateCreator(templateCreatorDialog: TemplateRef<any>) {\r\n    const selectedRequirements = this.getSelectedRequirements();\r\n    if (selectedRequirements.length === 0) {\r\n      this.message.showErrorMSG('請先選擇要加入模板的項目');\r\n      return;\r\n    }\r\n    this.dialogService.open(templateCreatorDialog);\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAyC,eAAe;AAE1E,SAASC,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/I,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAAmCC,uBAAuB,QAAQ,qEAAqE;AA6BhI,IAAMC,8BAA8B,GAApC,MAAMA,8BAA+B,SAAQZ,aAAa;EAC/Da,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB;IAE9B,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IAMpB;IACA,KAAAC,yBAAyB,GAAG,EAA8D;IAC1F,KAAAC,qBAAqB,GAA8B,EAAE;IACrD;IACA,KAAAC,aAAa,GAA8B,EAAE;IAC7C,KAAAC,eAAe,GAA4B,EAAE;IAC7C,KAAAC,eAAe,GAAgD;MAAEC,UAAU,EAAE;IAAE,CAAE;IAEjF,KAAAC,aAAa,GAAG,CACd;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAAC,SAAS,GAAG,IAAI,CAAClB,UAAU,CAACmB,cAAc,CAACxB,aAAa,CAAC;IACzD,KAAAyB,KAAK,GAAG,KAAK;IACb,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IAkPhB;IACQ,KAAAC,gBAAgB,GAAG,IAAI;IArQ7B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAoBSC,QAAQA,CAAA,GAAW;EAC5B;EACAF,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACf,yBAAyB,CAACkB,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAAClB,yBAAyB,CAACmB,OAAO,GAAG,IAAI;IAC7C,IAAI,CAACnB,yBAAyB,CAACoB,YAAY,GAAG,EAAE;IAChD,IAAI,CAACpB,yBAAyB,CAACqB,UAAU,GAAG,EAAE;IAC9C;IACA,IAAI,CAACrB,yBAAyB,CAACK,UAAU,GAAG,IAAI,CAACI,SAAS,CAACa,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAChB,KAAK,CAAC;EACpF;EAEA;EACAiB,WAAWA,CAAA;IACT,IAAI,CAACT,oBAAoB,EAAE;IAC3B;IACA,IAAI,IAAI,CAACb,aAAa,IAAI,IAAI,CAACA,aAAa,CAACuB,MAAM,GAAG,CAAC,EAAE;MACvDC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACb,UAAU,KAAK,CAAC,EAAE;UACzB,IAAI,CAACb,yBAAyB,CAAC2B,YAAY,GAAG,IAAI,CAACzB,aAAa,CAAC,CAAC,CAAC,CAAC0B,GAAG;QACzE,CAAC,MAAM;UACL,IAAI,CAAC5B,yBAAyB,CAAC2B,YAAY,GAAG,CAAC;QACjD;QACA,IAAI,CAACE,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEAC,YAAYA,CAACC,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAAC5B,SAAS,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,KAAK,IAAI6B,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAAC7B,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAO0B,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAChD,KAAK,CAACiD,KAAK,EAAE;IAElB;IACA,IAAI,IAAI,CAAC9B,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,CAACnB,KAAK,CAACkD,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACxC,eAAe,CAACuB,YAAY,CAAC;IAClE;IAEA,IAAI,CAACjC,KAAK,CAACkD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxC,eAAe,CAACgB,YAAY,CAAC;IAC9D,IAAI,CAAC1B,KAAK,CAACkD,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACxC,eAAe,CAACC,UAAU,CAAC;IAC7D,IAAI,CAACX,KAAK,CAACkD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxC,eAAe,CAACyC,KAAK,CAAC;IACvD,IAAI,CAACnD,KAAK,CAACkD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxC,eAAe,CAACc,OAAO,CAAC;IACzD,IAAI,CAACxB,KAAK,CAACkD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxC,eAAe,CAAC0C,UAAU,CAAC;IAC5D,IAAI,CAACpD,KAAK,CAACkD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxC,eAAe,CAAC2C,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAAC3C,eAAe,CAACiB,UAAU,IAAI,IAAI,CAACjB,eAAe,CAACiB,UAAU,CAACI,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAAC/B,KAAK,CAACsD,aAAa,CAACR,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACpC,eAAe,CAAC6C,OAAO,IAAI,IAAI,CAAC7C,eAAe,CAAC6C,OAAO,CAACxB,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAAC/B,KAAK,CAACsD,aAAa,CAACR,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEAU,GAAGA,CAACC,MAAwB;IAC1B,IAAI,CAACxC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACP,eAAe,GAAG;MAAEC,UAAU,EAAE,EAAE;MAAEc,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACf,eAAe,CAACc,OAAO,GAAG,CAAC;IAChC,IAAI,CAACd,eAAe,CAAC0C,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAACjC,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,IAAI,CAACD,gBAAgB,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACR,eAAe,CAACuB,YAAY,GAAG,IAAI,CAACf,gBAAgB;MAC3D,CAAC,MAAM,IAAI,IAAI,CAACV,aAAa,IAAI,IAAI,CAACA,aAAa,CAACuB,MAAM,GAAG,CAAC,EAAE;QAC9D,IAAI,CAACrB,eAAe,CAACuB,YAAY,GAAG,IAAI,CAACzB,aAAa,CAAC,CAAC,CAAC,CAAC0B,GAAG;MAC/D;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACxB,eAAe,CAACuB,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAACnC,aAAa,CAAC4D,IAAI,CAACD,MAAM,CAAC;EACjC;EAEME,MAAMA,CAACC,IAA2B,EAAEH,MAAwB;IAAA,IAAAI,KAAA;IAAA,OAAAC,iBAAA;MAChED,KAAI,CAACtD,qBAAqB,CAACwD,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAAC5C,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM4C,KAAI,CAACG,OAAO,EAAE;QACpBH,KAAI,CAAC/D,aAAa,CAAC4D,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEAG,IAAIA,CAACC,GAAQ;IACX,IAAI,CAACrB,UAAU,EAAE;IACjB,IAAI,IAAI,CAAChD,KAAK,CAACsD,aAAa,CAACvB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAChC,OAAO,CAACuE,aAAa,CAAC,IAAI,CAACtE,KAAK,CAACsD,aAAa,CAAC;MACpD;IACF;IAEA;IACA,IAAI,IAAI,CAACnC,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACT,eAAe,CAACuB,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAAC/B,kBAAkB,CAACqE,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAAC9D;KACZ,CAAC,CAAC+D,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC5E,OAAO,CAAC6E,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACzC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACpC,OAAO,CAAC8E,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACU,KAAK,EAAE;EACb;EAEAC,QAAQA,CAACpB,IAA2B;IAClC,IAAI,CAAClD,eAAe,CAACqD,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAAC9C,KAAK,GAAG,KAAK;IAClB,IAAIgE,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAACjF,kBAAkB,CAACkF,iCAAiC,CAAC;MACxDZ,IAAI,EAAE;QACJT,cAAc,EAAE,IAAI,CAACrD,eAAe,CAACqD;;KAExC,CAAC,CAACU,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAAC3E,OAAO,CAAC6E,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACzC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAb,gBAAgBA,CAAA;IACd,IAAI,CAACrB,gBAAgB,CAACoF,qCAAqC,CAAC;MAAEb,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEc,IAAI,CAACvG,kBAAkB,CAAC,IAAI,CAACsB,UAAU,CAAC,CAAC,CAACoE,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAAClE,aAAa,GAAGkE,GAAG,CAACa,OAAQ;MACjC;MACA,IAAI,IAAI,CAACpE,UAAU,KAAK,CAAC,IAAI,IAAI,CAACX,aAAa,CAACuB,MAAM,GAAG,CAAC,EAAE;QAC1D,IAAI,CAACzB,yBAAyB,CAAC2B,YAAY,GAAG,IAAI,CAACzB,aAAa,CAAC,CAAC,CAAC,CAAC0B,GAAG;QACvE,IAAI,CAACC,OAAO,EAAE;MAChB,CAAC,MAAM,IAAI,IAAI,CAAChB,UAAU,KAAK,CAAC,EAAE;QAChC,IAAI,CAACb,yBAAyB,CAAC2B,YAAY,GAAG,CAAC;QAC/C,IAAI,CAACE,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAAC7B,yBAAyB,CAACkF,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAACnF,yBAAyB,CAACoF,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAAClF,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAACmF,YAAY,GAAG,CAAC;IACrB;IACA,IAAI,IAAI,CAACzE,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACb,yBAAyB,CAAC2B,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAAC3B,yBAAyB,CAAC2B,YAAY,IAAI,IAAI,CAAC3B,yBAAyB,CAAC2B,YAAY,IAAI,CAAC,EAAE;QACnG,IAAI,CAACf,gBAAgB,GAAG,IAAI,CAACZ,yBAAyB,CAAC2B,YAAY;MACrE;IACF;IAEA,IAAI,CAAC/B,kBAAkB,CAAC2F,8BAA8B,CAAC;MAAErB,IAAI,EAAE,IAAI,CAAClE;IAAyB,CAAE,CAAC,CAC7FgF,IAAI,EAAE,CACNb,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACa,OAAO,EAAE;UACf;UACA,IAAI,CAAC9E,eAAe,GAAGiE,GAAG,CAACa,OAAO,CAAC3D,GAAG,CAACkE,IAAI,KAAK;YAC9C,GAAGA,IAAI;YACPC,QAAQ,EAAE;WACX,CAAC,CAAC;UACH,IAAI,CAACH,YAAY,GAAGlB,GAAG,CAACsB,UAAW;QACrC;MACF;IACF,CAAC,CAAC;EACN;EAAEhC,OAAOA,CAAA;IACP,IAAI,CAAC9D,kBAAkB,CAAC+F,8BAA8B,CAAC;MAAEzB,IAAI,EAAE,IAAI,CAACjE;IAAqB,CAAE,CAAC,CACzF+E,IAAI,EAAE,CACNb,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACa,OAAO,EAAE;UACf,IAAI,CAAC7E,eAAe,GAAG;YAAEC,UAAU,EAAE,EAAE;YAAEc,OAAO,EAAE;UAAK,CAAE;UACzD,IAAI,CAACf,eAAe,CAACuB,YAAY,GAAGyC,GAAG,CAACa,OAAO,CAACtD,YAAY;UAC5D,IAAI,CAACvB,eAAe,CAACiB,UAAU,GAAG+C,GAAG,CAACa,OAAO,CAAC5D,UAAU;UACxD,IAAI,CAACjB,eAAe,CAACC,UAAU,GAAG+D,GAAG,CAACa,OAAO,CAAC5E,UAAU,GAAI2B,KAAK,CAACC,OAAO,CAACmC,GAAG,CAACa,OAAO,CAAC5E,UAAU,CAAC,GAAG+D,GAAG,CAACa,OAAO,CAAC5E,UAAU,GAAG,CAAC+D,GAAG,CAACa,OAAO,CAAC5E,UAAU,CAAC,GAAI,EAAE;UAC3J,IAAI,CAACD,eAAe,CAAC6C,OAAO,GAAGmB,GAAG,CAACa,OAAO,CAAChC,OAAO;UAClD,IAAI,CAAC7C,eAAe,CAACgB,YAAY,GAAGgD,GAAG,CAACa,OAAO,CAAC7D,YAAY;UAC5D,IAAI,CAAChB,eAAe,CAACqD,cAAc,GAAGW,GAAG,CAACa,OAAO,CAACxB,cAAc;UAChE,IAAI,CAACrD,eAAe,CAACyC,KAAK,GAAGuB,GAAG,CAACa,OAAO,CAACpC,KAAK;UAC9C,IAAI,CAACzC,eAAe,CAACc,OAAO,GAAGkD,GAAG,CAACa,OAAO,CAAC/D,OAAO;UAClD,IAAI,CAACd,eAAe,CAAC0C,UAAU,GAAGsB,GAAG,CAACa,OAAO,CAACnC,UAAU;UACxD,IAAI,CAAC1C,eAAe,CAAC2C,KAAK,GAAGqB,GAAG,CAACa,OAAO,CAAClC,KAAK;UAC9C;UACA,IAAI,CAAC3C,eAAe,CAACe,OAAO,GAAIiD,GAAG,CAACa,OAAe,CAAC9D,OAAO,IAAI,KAAK;QACtE;MACF;IACF,CAAC,CAAC;EACN;EAEAyE,iBAAiBA,CAACrF,KAAa,EAAEsF,OAAY;IAC3CjC,OAAO,CAACC,GAAG,CAACgC,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAACzF,eAAe,CAACC,UAAU,EAAEyF,QAAQ,CAACvF,KAAK,CAAC,EAAE;QACrD,IAAI,CAACH,eAAe,CAACC,UAAU,EAAEmC,IAAI,CAACjC,KAAK,CAAC;MAC9C;MACAqD,OAAO,CAACC,GAAG,CAAC,IAAI,CAACzD,eAAe,CAACC,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACD,eAAe,CAACC,UAAU,GAAG,IAAI,CAACD,eAAe,CAACC,UAAU,EAAE0F,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKzF,KAAK,CAAC;IAC7F;EACF;EAEA0F,cAAcA,CAAC3C,IAAS;IACtB,OAAOA,IAAI,CAACnC,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAIA+E,WAAWA,CAACC,KAAU;IACpB;IACA,IAAI,IAAI,CAACrF,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC7B;IACF;IACA;IACA,IAAIqF,KAAK,CAACC,QAAQ,KAAK,IAAI,EAAE;MAC3B,IAAI,CAACvF,UAAU,GAAG,CAAC;MACnB,IAAI,CAACb,yBAAyB,CAAC2B,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,IAAI,CAACd,UAAU,GAAG,CAAC;MACnB;MACA,IAAI,IAAI,CAACX,aAAa,IAAI,IAAI,CAACA,aAAa,CAACuB,MAAM,GAAG,CAAC,EAAE;QACvD,IAAI,CAACzB,yBAAyB,CAAC2B,YAAY,GAAG,IAAI,CAACzB,aAAa,CAAC,CAAC,CAAC,CAAC0B,GAAG;MACzE;IACF;IACA,IAAI,CAACC,OAAO,EAAE;EAChB;EAEA;EACAwE,WAAWA,CAAClD,MAAwB;IAClC,IAAI,CAACxC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACP,eAAe,GAAG;MAAEC,UAAU,EAAE,EAAE;MAAEc,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACf,eAAe,CAACc,OAAO,GAAG,CAAC;IAChC,IAAI,CAACd,eAAe,CAAC0C,UAAU,GAAG,CAAC;IACnC;IACA,IAAI,CAAC1C,eAAe,CAACuB,YAAY,GAAG,CAAC;IACrC,IAAI,CAACnC,aAAa,CAAC4D,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACMmD,cAAcA,CAAChD,IAA2B,EAAEH,MAAwB;IAAA,IAAAoD,MAAA;IAAA,OAAA/C,iBAAA;MACxE+C,MAAI,CAACtG,qBAAqB,CAACwD,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChE8C,MAAI,CAAC5F,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM4F,MAAI,CAAC7C,OAAO,EAAE;QACpB6C,MAAI,CAAC/G,aAAa,CAAC4D,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA;EACA6C,YAAYA,CAACzC,GAAQ;IACnB;IACA,IAAI,CAACrE,KAAK,CAACiD,KAAK,EAAE;IAClB,IAAI,CAACjD,KAAK,CAACkD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxC,eAAe,CAACgB,YAAY,CAAC;IAC9D,IAAI,CAAC1B,KAAK,CAACkD,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACxC,eAAe,CAACC,UAAU,CAAC;IAC7D,IAAI,CAACX,KAAK,CAACkD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxC,eAAe,CAACyC,KAAK,CAAC;IACvD,IAAI,CAACnD,KAAK,CAACkD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxC,eAAe,CAACc,OAAO,CAAC;IACzD,IAAI,CAACxB,KAAK,CAACkD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxC,eAAe,CAAC0C,UAAU,CAAC;IAC5D,IAAI,CAACpD,KAAK,CAACkD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxC,eAAe,CAAC2C,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAAC3C,eAAe,CAACiB,UAAU,IAAI,IAAI,CAACjB,eAAe,CAACiB,UAAU,CAACI,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAAC/B,KAAK,CAACsD,aAAa,CAACR,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACpC,eAAe,CAAC6C,OAAO,IAAI,IAAI,CAAC7C,eAAe,CAAC6C,OAAO,CAACxB,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAAC/B,KAAK,CAACsD,aAAa,CAACR,IAAI,CAAC,kBAAkB,CAAC;IACnD;IAEA,IAAI,IAAI,CAAC9C,KAAK,CAACsD,aAAa,CAACvB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAChC,OAAO,CAACuE,aAAa,CAAC,IAAI,CAACtE,KAAK,CAACsD,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMyD,YAAY,GAAG;MAAE,GAAG,IAAI,CAACrG;IAAe,CAAE;IAChDqG,YAAY,CAAC9E,YAAY,GAAG,CAAC;IAE7B,IAAI,CAAC/B,kBAAkB,CAACqE,+BAA+B,CAAC;MACtDC,IAAI,EAAEuC;KACP,CAAC,CAACtC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC5E,OAAO,CAAC6E,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACzC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACpC,OAAO,CAAC8E,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACU,KAAK,EAAE;EACb;EAEAiC,kBAAkBA,CAACC,oBAAsC;IACvD,IAAI,CAACnH,aAAa,CAAC4D,IAAI,CAACuD,oBAAoB,CAAC;EAC/C;EAEAC,gBAAgBA,CAACC,GAAa;IAC5B;IACAC,KAAK,CAAC,QAAQ,GAAGD,GAAG,CAACE,YAAY,CAAC;EACpC;EAEA;EACAC,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAAC7G,eAAe,CAAC4F,MAAM,CAACkB,GAAG,IAAIA,GAAG,CAACxB,QAAQ,CAAC;EACzD;EAEA;EACAyB,4BAA4BA,CAAA;IAC1B;EAAA;EAGF;EACAC,qBAAqBA,CAAA;IACnB,IAAI,CAAChH,eAAe,CAACgC,OAAO,CAAC8E,GAAG,IAAIA,GAAG,CAACxB,QAAQ,GAAG,IAAI,CAAC;EAC1D;EAEA;EACA2B,kBAAkBA,CAAA;IAChB,IAAI,CAACjH,eAAe,CAACgC,OAAO,CAAC8E,GAAG,IAAIA,GAAG,CAACxB,QAAQ,GAAG,KAAK,CAAC;EAC3D;EAEA;EACA4B,aAAaA,CAAA;IACX,OAAO,IAAI,CAAClH,eAAe,CAACsB,MAAM,GAAG,CAAC,IAAI,IAAI,CAACtB,eAAe,CAACmH,KAAK,CAACL,GAAG,IAAIA,GAAG,CAACxB,QAAQ,CAAC;EAC3F;EAEA;EACA8B,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG,IAAI,CAACrH,eAAe,CAAC4F,MAAM,CAACkB,GAAG,IAAIA,GAAG,CAACxB,QAAQ,CAAC,CAAChE,MAAM;IAC7E,OAAO+F,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAG,IAAI,CAACrH,eAAe,CAACsB,MAAM;EACzE;EAEA;EACAgG,eAAeA,CAACtB,KAAU;IACxB,MAAMuB,SAAS,GAAGvB,KAAK,CAACwB,MAAM,CAAC9B,OAAO;IACtC,IAAI,CAAC1F,eAAe,CAACgC,OAAO,CAAC8E,GAAG,IAAIA,GAAG,CAACxB,QAAQ,GAAGiC,SAAS,CAAC;EAC/D;EAEA;EACAE,mBAAmBA,CAACC,qBAAuC;IACzD,MAAMC,oBAAoB,GAAG,IAAI,CAACd,uBAAuB,EAAE;IAC3D,IAAIc,oBAAoB,CAACrG,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAChC,OAAO,CAAC8E,YAAY,CAAC,cAAc,CAAC;MACzC;IACF;IACA,IAAI,CAAC/E,aAAa,CAAC4D,IAAI,CAACyE,qBAAqB,CAAC;EAChD;CACD;AAjaYzI,8BAA8B,GAAA2I,UAAA,EAtB1C9J,SAAS,CAAC;EACT+J,QAAQ,EAAE,4BAA4B;EACtCC,UAAU,EAAE,IAAI;EAAEC,OAAO,EAAE,CACzBhK,YAAY,EACZQ,mBAAmB,EACnBN,aAAa,EACbO,WAAW,EACXL,cAAc,EACdD,cAAc,EACdQ,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVZ,gBAAgB,EAChBa,kBAAkB,EAClBC,oBAAoB,EACpBV,cAAc,EACdY,uBAAuB,CACxB;EACDgJ,WAAW,EAAE,yCAAyC;EACtDC,QAAQ,EAAE;CACX,CAAC,C,EACWhJ,8BAA8B,CAia1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}